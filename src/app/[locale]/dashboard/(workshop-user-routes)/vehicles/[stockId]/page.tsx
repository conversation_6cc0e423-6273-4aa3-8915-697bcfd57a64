import { getVehicleDetailById } from '../_actions/getVehicleDetailById';
import { initiateQrScanAction } from '../_actions/vehicleQrService';
import VehicleDetailClientPage from './client';
import { cookies } from 'next/headers';

export default async function VehicleDetailPage({ params, searchParams }: { params: { stockId: string }, searchParams: { [key: string]: string } }) {
  const vehicle = await getVehicleDetailById(params.stockId);
  if (!vehicle) {
    return <div>Vehicle not found</div>
  }

  // Get qr_scan_token from search params
  const qrScanToken = searchParams?.qr_scan_token;
  let qrModalInitialState = null;
  if (qrScanToken) {
    try {
      const response = await initiateQrScanAction(params.stockId, qrScanToken);
      qrModalInitialState = {
        isOpen: true,
        vehicleId: params.stockId,
        currentStatus: response.currentPhysicalStatus || 'N/A',
        nextStatusToDisplay: response.nextPhysicalStatusToDisplay || null,
        nextStepOptions: response.nextStepOptions || [],
        confirmationToken: response.confirmationToken || null,
        message: response.message || null,
        actionAvailable: response.actionAvailable,
      };
    } catch (error: any) {
      qrModalInitialState = {
        isOpen: true,
        vehicleId: params.stockId,
        currentStatus: 'N/A',
        nextStatusToDisplay: null,
        nextStepOptions: [],
        confirmationToken: null,
        message: error?.message || 'Failed to process QR scan.',
        actionAvailable: false,
      };
    }
  }

  return (
    <>
      <VehicleDetailClientPage params={params} vehicle={vehicle} qrModalInitialState={qrModalInitialState} />
    </>
  )
}
